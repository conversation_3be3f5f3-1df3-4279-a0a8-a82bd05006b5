"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { restrictToVerticalAxis } from "@dnd-kit/modifiers"
import {
  SortableContext,
  array<PERSON>ove,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import {
  ChevronDown,
  ChevronRight,
  GripVertical,
  Bot,
  Plus,
  Sparkles,
  ArrowUp,
  ArrowDown
} from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterBlock, NewsletterHeaderFooter } from "@/types/newsletter"
import { Block } from "@/types/block"
import { HeaderFooterItem } from "./header-footer-item"
import { AddBlockModal } from "../modals/add-block-modal"

interface BlockAccordionProps {
  blocks: NewsletterBlock[]
  headers?: NewsletterHeaderFooter[]
  footers?: NewsletterHeaderFooter[]
  onBlocksReorder: (blocks: NewsletterBlock[]) => void
  onBlockAIClick: (block: NewsletterBlock) => void
  onHeadersReorder?: (headers: NewsletterHeaderFooter[]) => void
  onFootersReorder?: (footers: NewsletterHeaderFooter[]) => void
  onHeaderFooterAIClick?: (headerFooter: NewsletterHeaderFooter, type: 'header' | 'footer') => void
  onAddBlock?: (block: Block) => void
  onVariableChange?: (blockId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (blockId: string, htmlContent: string) => void
  onHeaderFooterVariableChange?: (headerFooterId: string, variableId: string, language: string, value: string) => void
  onHeaderFooterHtmlContentChange?: (headerFooterId: string, htmlContent: string) => void
  brandName?: string
  brand?: string
}

interface SortableBlockItemProps {
  block: NewsletterBlock
  isExpanded: boolean
  onToggle: () => void
  onAIClick: () => void
  onVariableChange?: (blockId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (blockId: string, htmlContent: string) => void
  isDragging?: boolean
}

interface SortableHeaderFooterItemProps {
  headerFooter: NewsletterHeaderFooter
  type: 'header' | 'footer'
  isExpanded: boolean
  onToggle: () => void
  onAIClick: () => void
  onVariableChange?: (headerFooterId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (headerFooterId: string, htmlContent: string) => void
  isDragging?: boolean
}

function SortableBlockItem({
  block,
  isExpanded,
  onToggle,
  onAIClick,
  onVariableChange,
  onHtmlContentChange,
  isDragging
}: SortableBlockItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: block.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const isCurrentlyDragging = isDragging || sortableIsDragging

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative",
        isCurrentlyDragging && "z-10 opacity-80"
      )}
    >
      <Card className={cn("transition-colors", !block.is_visible && "opacity-60")}>
        <Collapsible open={isExpanded}>
          <CollapsibleTrigger asChild>
            <CardHeader
              className="cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={onToggle}
            >
              <div className="flex items-center gap-3">
                {/* Drag Handle */}
                <Button
                  {...attributes}
                  {...listeners}
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 cursor-grab active:cursor-grabbing"
                  onClick={(e) => e.stopPropagation()}
                >
                  <GripVertical className="h-4 w-4 text-muted-foreground" />
                </Button>

                {/* Expand/Collapse Icon */}
                <div className="flex-shrink-0">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>

                {/* Block Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm truncate">{block.name}</CardTitle>
                    <div className="flex items-center gap-1">
                      <Badge variant="outline" className="text-xs">
                        #{block.order_position}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription className="text-xs truncate">
                    {block.description || "No description"}
                  </CardDescription>
                </div>

                {/* AI Button */}
                <Button
                  variant="outline"
                  size="icon"
                  className="flex shadow-lg text-yellow-400  transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-110 group animate-pulse-gradient hover:text-white"
                  onClick={(e) => {
                    e.stopPropagation()
                    onAIClick()
                  }}
                >
                  <Sparkles className="h-6 w-6 group-hover:animate-pulse" />
                </Button>
              </div>
            </CardHeader>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <CardContent className="pt-0">
              <Separator className="mb-4" />

              {/* Block Details */}
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium mb-2">Variables</h4>
                  {block.variable_values && block.variable_values.length > 0 ? (
                    <div className="space-y-4">
                      {block.variable_values.map((variable) => (
                        <div key={variable.id} className="border rounded-lg p-3">
                          <div className="font-medium text-sm mb-2">
                            {variable.name} ({variable.variable_type_display_name})
                          </div>
                          <Tabs defaultValue="es" className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                              <TabsTrigger value="es">ES</TabsTrigger>
                              <TabsTrigger value="ca">CA</TabsTrigger>
                              <TabsTrigger value="fr">FR</TabsTrigger>
                              <TabsTrigger value="en">EN</TabsTrigger>
                            </TabsList>
                            {Object.entries(variable.value).map(([lang, value]) => (
                              <TabsContent key={lang} value={lang} className="mt-2">
                                <div className="space-y-1">
                                  <Label htmlFor={`${variable.id}-${lang}`} className="text-xs">
                                    {lang.toUpperCase()}
                                  </Label>
                                  {variable.variable_type_field_type === 'text' ? (
                                    <Textarea
                                      id={`${variable.id}-${lang}`}
                                      value={value || ''}
                                      onChange={(e) => onVariableChange?.(block.id, variable.id, lang, e.target.value)}
                                      className="min-h-[60px] text-xs"
                                      placeholder={`Enter ${variable.variable_type_display_name} in ${lang.toUpperCase()}`}
                                    />
                                  ) : (
                                    <Input
                                      id={`${variable.id}-${lang}`}
                                      value={value || ''}
                                      onChange={(e) => onVariableChange?.(block.id, variable.id, lang, e.target.value)}
                                      className="text-xs"
                                      placeholder={`Enter ${variable.variable_type_display_name} in ${lang.toUpperCase()}`}
                                    />
                                  )}
                                </div>
                              </TabsContent>
                            ))}
                          </Tabs>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-xs text-muted-foreground">No variables defined</p>
                  )}
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">HTML Content</h4>
                  <div className="space-y-2">
                    <Textarea
                      value={block.html_content || ''}
                      onChange={(e) => onHtmlContentChange?.(block.id, e.target.value)}
                      className="min-h-[120px] font-mono text-xs"
                      placeholder="Enter HTML content for this block"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  )
}

function SortableHeaderFooterItem({
  headerFooter,
  type,
  isExpanded,
  onToggle,
  onAIClick,
  onVariableChange,
  onHtmlContentChange,
  isDragging
}: SortableHeaderFooterItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: headerFooter.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const isCurrentlyDragging = isDragging || sortableIsDragging

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative",
        isCurrentlyDragging && "z-10 opacity-80"
      )}
      {...attributes}
      {...listeners}
    >
      <HeaderFooterItem
        headerFooter={headerFooter}
        type={type}
        isExpanded={isExpanded}
        onToggle={onToggle}
        onAIClick={onAIClick}
        onVariableChange={onVariableChange}
        onHtmlContentChange={onHtmlContentChange}
        isDragging={isCurrentlyDragging}
      />
    </div>
  )
}

export function BlockAccordion({
  blocks,
  headers = [],
  footers = [],
  onBlocksReorder,
  onBlockAIClick,
  onHeadersReorder,
  onFootersReorder,
  onHeaderFooterAIClick,
  onAddBlock,
  onVariableChange,
  onHtmlContentChange,
  onHeaderFooterVariableChange,
  onHeaderFooterHtmlContentChange,
  brandName,
  brand
}: BlockAccordionProps) {
  const [expandedBlocks, setExpandedBlocks] = useState<Set<string>>(new Set())
  const [expandedHeaders, setExpandedHeaders] = useState<Set<string>>(new Set())
  const [expandedFooters, setExpandedFooters] = useState<Set<string>>(new Set())
  const [isDragging, setIsDragging] = useState(false)
  const [addBlockModalOpen, setAddBlockModalOpen] = useState(false)

  const sensors = useSensors(
    useSensor(MouseSensor, { activationConstraint: { distance: 5 } }),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor)
  )

  const handleDragStart = () => {
    setIsDragging(true)
    // Collapse all blocks during drag
    setExpandedBlocks(new Set())
  }

  const handleDragEnd = (event: DragEndEvent) => {
    setIsDragging(false)

    const { active, over } = event
    if (!active || !over) return

    const activeId = active.id as string
    const overId = over.id as string

    if (activeId !== overId) {
      // Check if we're reordering blocks
      const blockOldIndex = blocks.findIndex(block => block.id === activeId)
      const blockNewIndex = blocks.findIndex(block => block.id === overId)

      if (blockOldIndex !== -1 && blockNewIndex !== -1) {
        try {
          const reorderedBlocks = arrayMove(blocks, blockOldIndex, blockNewIndex)
          // Update order positions
          const updatedBlocks = reorderedBlocks.map((block, index) => ({
            ...block,
            order_position: index + 1
          }))
          onBlocksReorder(updatedBlocks)
        } catch (error) {
          console.error('Error reordering blocks:', error)
          setIsDragging(false)
        }
        return
      }

      // Check if we're reordering headers
      const headerOldIndex = headers.findIndex(header => header.id === activeId)
      const headerNewIndex = headers.findIndex(header => header.id === overId)

      if (headerOldIndex !== -1 && headerNewIndex !== -1) {
        try {
          const reorderedHeaders = arrayMove(headers, headerOldIndex, headerNewIndex)
          onHeadersReorder?.(reorderedHeaders)
        } catch (error) {
          console.error('Error reordering headers:', error)
          setIsDragging(false)
        }
        return
      }

      // Check if we're reordering footers
      const footerOldIndex = footers.findIndex(footer => footer.id === activeId)
      const footerNewIndex = footers.findIndex(footer => footer.id === overId)

      if (footerOldIndex !== -1 && footerNewIndex !== -1) {
        try {
          const reorderedFooters = arrayMove(footers, footerOldIndex, footerNewIndex)
          onFootersReorder?.(reorderedFooters)
        } catch (error) {
          console.error('Error reordering footers:', error)
          setIsDragging(false)
        }
        return
      }
    }
  }

  const toggleBlockExpansion = (blockId: string) => {
    if (isDragging) return // Don't allow expansion during drag

    setExpandedBlocks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(blockId)) {
        newSet.delete(blockId)
      } else {
        newSet.add(blockId)
      }
      return newSet
    })
  }

  const toggleHeaderExpansion = (headerId: string) => {
    setExpandedHeaders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(headerId)) {
        newSet.delete(headerId)
      } else {
        newSet.add(headerId)
      }
      return newSet
    })
  }

  const toggleFooterExpansion = (footerId: string) => {
    setExpandedFooters(prev => {
      const newSet = new Set(prev)
      if (newSet.has(footerId)) {
        newSet.delete(footerId)
      } else {
        newSet.add(footerId)
      }
      return newSet
    })
  }

  const handleKeyboardToggle = (blockId: string, event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      toggleBlockExpansion(blockId)
    }
  }

  const handleBlockSelect = (block: Block) => {
    onAddBlock?.(block)
  }

  const sortedBlocks = (blocks || []).slice().sort((a, b) => a.order_position - b.order_position)

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Newsletter Content</h3>
          <p className="text-sm text-muted-foreground">
            {headers.length} header{headers.length !== 1 ? 's' : ''}, {blocks.length} block{blocks.length !== 1 ? 's' : ''}, {footers.length} footer{footers.length !== 1 ? 's' : ''}
            {brandName && ` for ${brandName}`}
          </p>
        </div>
        <div className="flex gap-2">
          {onAddBlock && (
            <Button variant="outline" size="sm" onClick={() => setAddBlockModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Block
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* Headers Section */}
        {headers.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <ArrowUp className="h-4 w-4" />
              Headers
            </h4>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis]}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={headers.map(header => header.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {headers.map((header) => (
                    <SortableHeaderFooterItem
                      key={header.id}
                      headerFooter={header}
                      type="header"
                      isExpanded={expandedHeaders.has(header.id)}
                      onToggle={() => toggleHeaderExpansion(header.id)}
                      onAIClick={() => onHeaderFooterAIClick?.(header, 'header')}
                      onVariableChange={onHeaderFooterVariableChange}
                      onHtmlContentChange={onHeaderFooterHtmlContentChange}
                      isDragging={isDragging}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}

        {/* Blocks Section */}
        <div className="space-y-2">
          {blocks.length > 0 && (
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Bot className="h-4 w-4" />
              Content Blocks
            </h4>
          )}

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            modifiers={[restrictToVerticalAxis]}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortedBlocks.map(block => block.id)} strategy={verticalListSortingStrategy}>
              <div className="space-y-2">
                {sortedBlocks.map((block) => (
                  <SortableBlockItem
                    key={block.id}
                    block={block}
                    isExpanded={expandedBlocks.has(block.id)}
                    onToggle={() => toggleBlockExpansion(block.id)}
                    onAIClick={() => onBlockAIClick(block)}
                    onVariableChange={onVariableChange}
                    onHtmlContentChange={onHtmlContentChange}
                    isDragging={isDragging}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>

        {/* Footers Section */}
        {footers.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <ArrowDown className="h-4 w-4" />
              Footers
            </h4>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis]}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={footers.map(footer => footer.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {footers.map((footer) => (
                    <SortableHeaderFooterItem
                      key={footer.id}
                      headerFooter={footer}
                      type="footer"
                      isExpanded={expandedFooters.has(footer.id)}
                      onToggle={() => toggleFooterExpansion(footer.id)}
                      onAIClick={() => onHeaderFooterAIClick?.(footer, 'footer')}
                      onVariableChange={onHeaderFooterVariableChange}
                      onHtmlContentChange={onHeaderFooterHtmlContentChange}
                      isDragging={isDragging}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}
      </div>

      {/* Add Block Modal */}
      <AddBlockModal
        open={addBlockModalOpen}
        onOpenChange={setAddBlockModalOpen}
        brand={brand}
        onBlockSelect={handleBlockSelect}
      />
    </div>
  )
}
