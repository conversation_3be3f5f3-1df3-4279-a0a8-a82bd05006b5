"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChevronDown,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  Sparkles,
  GripVertical,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterHeaderFooter } from "@/types/newsletter"
import { t, tt } from "@/lib/translations"

interface HeaderFooterItemProps {
  headerFooter: NewsletterHeaderFooter
  isExpanded: boolean
  onToggle: () => void
  onVariableChange?: (headerFooterId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (headerFooterId: string, htmlContent: string) => void
  onAIClick?: () => void
  type: 'header' | 'footer'
  isDragging?: boolean
}

export function HeaderFooterItem({
  headerFooter,
  isExpanded,
  onToggle,
  onVariableChange,
  onHtmlContentChange,
  onAIClick,
  type,
  isDragging
}: HeaderFooterItemProps) {
  const isHeader = type === 'header'

  return (
    <Card className={cn("transition-colors", isDragging && "opacity-80")}>
      <Collapsible open={isExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={onToggle}
          >
            <div className="flex items-center gap-3">
              {/* Drag Handle */}
              <div className="flex-shrink-0">
                <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab active:cursor-grabbing" />
              </div>

              {/* Expand/Collapse Icon */}
              <div className="flex-shrink-0">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>

              {/* Header/Footer Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm truncate">{headerFooter.name}</CardTitle>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="text-xs">
                      {headerFooter.element_type_display}
                    </Badge>
                  </div>
                </div>
                <CardDescription className="text-xs truncate">
                  {isHeader ? t('newsletter.newsletterHeader') : t('newsletter.newsletterFooter')}
                </CardDescription>
              </div>

              {/* AI Button */}
              {onAIClick && (
                <Button
                  variant="outline"
                  size="icon"
                  className="flex shadow-lg text-yellow-400 transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-110 group animate-pulse-gradient hover:text-white"
                  onClick={(e) => {
                    e.stopPropagation()
                    onAIClick()
                  }}
                >
                  <Sparkles className="h-6 w-6 group-hover:animate-pulse" />
                </Button>
              )}
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            <Separator className="mb-4" />

            {/* Header/Footer Details */}
            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium mb-2">{t('newsletter.variables')}</h4>
                {headerFooter.variable_values && headerFooter.variable_values.length > 0 ? (
                  <div className="space-y-4">
                    {headerFooter.variable_values.map((variable) => (
                      <div key={variable.id} className="border rounded-lg p-3">
                        <div className="font-medium text-sm mb-2">
                          {variable.name} ({variable.variable_type_display_name})
                        </div>
                        <Tabs defaultValue="es" className="w-full">
                          <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="es">ES</TabsTrigger>
                            <TabsTrigger value="ca">CA</TabsTrigger>
                            <TabsTrigger value="fr">FR</TabsTrigger>
                            <TabsTrigger value="en">EN</TabsTrigger>
                          </TabsList>
                          {Object.entries(variable.value).map(([lang, value]) => (
                            <TabsContent key={lang} value={lang} className="mt-2">
                              <div className="space-y-1">
                                <Label htmlFor={`${variable.id}-${lang}`} className="text-xs">
                                  {lang.toUpperCase()}
                                </Label>
                                {variable.variable_type_field_type === 'text' ? (
                                  <Textarea
                                    id={`${variable.id}-${lang}`}
                                    value={value || ''}
                                    onChange={(e) => onVariableChange?.(headerFooter.id, variable.id, lang, e.target.value)}
                                    className="min-h-[60px] text-xs"
                                    placeholder={tt('newsletter.enterVariableIn')(variable.variable_type_display_name, lang.toUpperCase())}
                                  />
                                ) : (
                                  <Input
                                    id={`${variable.id}-${lang}`}
                                    value={value || ''}
                                    onChange={(e) => onVariableChange?.(headerFooter.id, variable.id, lang, e.target.value)}
                                    className="text-xs"
                                    placeholder={tt('newsletter.enterVariableIn')(variable.variable_type_display_name, lang.toUpperCase())}
                                  />
                                )}
                              </div>
                            </TabsContent>
                          ))}
                        </Tabs>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">{t('newsletter.noVariablesDefined')}</p>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">{t('newsletter.htmlContent')}</h4>
                <div className="space-y-2">
                  <Textarea
                    value={headerFooter.html_content || ''}
                    onChange={(e) => onHtmlContentChange?.(headerFooter.id, e.target.value)}
                    className="min-h-[120px] font-mono text-xs"
                    placeholder={t('newsletter.enterHtmlContentHeaderFooter')}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
