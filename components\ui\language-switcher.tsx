"use client"

import React from "react"
import { useLanguages } from "@/hooks/use-languages"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { t } from "@/lib/translations"

export function LanguageSwitcher() {
  const { languages, loading } = useLanguages()
  const [selectedLanguage, setSelectedLanguage] = React.useState("")

  if (loading) {
    return (
      <Select disabled>
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder={t('newsletter.loading')} />
        </SelectTrigger>
      </Select>
    )
  }

  return (
    <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
      <SelectTrigger className="w-[120px]">
        <SelectValue placeholder={t('newsletter.language')} />
      </SelectTrigger>
      <SelectContent>
        {languages.map((language) => (
          <SelectItem key={language.language} value={language.language}>
            {language.language_display}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
