"use client"

import React from "react"
import { useLanguages } from "@/hooks/use-languages"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export function LanguageSwitcher() {
  const { languages, loading } = useLanguages()
  const [selectedLanguage, setSelectedLanguage] = React.useState("")

  if (loading) {
    return (
      <Select disabled>
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder="Loading..." />
        </SelectTrigger>
      </Select>
    )
  }

  return (
    <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
      <SelectTrigger className="w-[120px]">
        <SelectValue placeholder="Language" />
      </SelectTrigger>
      <SelectContent>
        {languages.map((language) => (
          <SelectItem key={language.language} value={language.language}>
            {language.language_display}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
