"use client"

import { useState, useEffect, useCallback } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { NextPage } from 'next'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Settings, Eye, Send, Bot, Save, AlertCircle, Sparkles, Languages } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import Link from 'next/link'
import { toast } from 'sonner'

// Import our new components
import { useNewsletterBuilder } from '@/hooks/use-newsletter-builder'
import { BlockAccordion } from '@/components/newsletter/block-accordion'
import { HTMLPreview } from '@/components/newsletter/html-preview'
import { AIChatDrawer } from '@/components/newsletter/ai-chat-drawer'
import { NewsletterBlock, NewsletterBuilderData, NewsletterHeaderFooter } from '@/types/newsletter'
import { Block } from '@/types/block'

interface Props { }

const NewsletterBuilderPage: NextPage<Props> = ({ }) => {
  const params = useParams()
  const newsletterId = params.id as string

  // State for AI drawer
  const [aiDrawerOpen, setAiDrawerOpen] = useState(false)
  const [selectedBlock, setSelectedBlock] = useState<NewsletterBlock | null>(null)
  const [aiDebugInfo, setAiDebugInfo] = useState<Record<string, any> | undefined>()

  // Local editing state
  const [localNewsletterData, setLocalNewsletterData] = useState<NewsletterBuilderData | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [saving, setSaving] = useState(false)

  // Newsletter data hook
  const {
    newsletterData,
    loading,
    error,
    refetch,
    updateNewsletter,
    updating,
    updateError
  } = useNewsletterBuilder({ newsletterId })

  // Sync local data with fetched data
  useEffect(() => {
    if (newsletterData && !localNewsletterData) {
      setLocalNewsletterData(JSON.parse(JSON.stringify(newsletterData))) // Deep copy
    }
  }, [newsletterData, localNewsletterData])

  // Use local data for display, fallback to fetched data
  const displayData = localNewsletterData || newsletterData

  const handleSave = useCallback(async () => {
    if (!displayData || !hasUnsavedChanges) return

    setSaving(true)
    try {
      await updateNewsletter({
        newsletter_parent_id: newsletterId,
        name: displayData.name,
        status: displayData.status,
        nl_blocks: displayData.nl_blocks,
        headers: displayData.headers,
        footers: displayData.footers
      })

      toast.success('Newsletter saved successfully')
      setHasUnsavedChanges(false)
      refetch() // Refresh data from server
    } catch (err) {
      console.error('Error saving newsletter:', err)
    } finally {
      setSaving(false)
    }
  }, [displayData, hasUnsavedChanges, newsletterId, updateNewsletter, refetch])

  // Warn about unsaved changes when navigating away
  const handleBeforeUnload = useCallback((e: BeforeUnloadEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault()
      e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
      return 'You have unsaved changes. Are you sure you want to leave?'
    }
  }, [hasUnsavedChanges])

  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [handleBeforeUnload])

  // Keyboard shortcut for saving (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (hasUnsavedChanges && !saving) {
          handleSave()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [hasUnsavedChanges, saving, handleSave])

  const handleBlocksReorder = (reorderedBlocks: NewsletterBlock[]) => {
    if (!displayData) return

    const updatedBlocks = reorderedBlocks.map((block, index) => ({
      ...block,
      order_position: index + 1
    }))

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
  }

  const handleBlockAIClick = (block: NewsletterBlock) => {
    setSelectedBlock(block)
    setAiDebugInfo({
      blockId: block.id,
      blockName: block.name,
      orderPosition: block.order_position,
      isVisible: block.is_visible,
      variableCount: block.variable_values?.length || 0,
      variables: block.variable_values?.map(v => ({
        name: v.name,
        type: v.variable_type_display_name,
        hasValues: Object.values(v.value).some(val => val.trim() !== '')
      })) || []
    })
    setAiDrawerOpen(true)
  }

  const handleGlobalAIClick = () => {
    setSelectedBlock(null)
    setAiDebugInfo({
      newsletterId,
      totalBlocks: newsletterData?.nl_blocks?.length || 0,
      visibleBlocks: newsletterData?.nl_blocks?.filter(b => b.is_visible)?.length || 0,
      brand: newsletterData?.brand_name
    })
    setAiDrawerOpen(true)
  }

  const handleAddBlock = (block: Block) => {
    if (!displayData) return

    // Convert Block to NewsletterBlock format
    const newNewsletterBlock: NewsletterBlock = {
      id: block.id,
      name: block.name,
      description: block.description,
      html_content: block.html_content,
      order_position: displayData.nl_blocks.length + 1,
      is_visible: true,
      variable_values: block.variables.map(variable => ({
        id: variable.id,
        variable_type_id: variable.variable_type,
        variable_type_name: variable.name,
        variable_type_display_name: variable.name,
        variable_type_field_type: 'text', // Default field type
        name: variable.name,
        value: variable.default_value
      })),
      last_edited_by: null,
      last_edited_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const updatedBlocks = [...displayData.nl_blocks, newNewsletterBlock]

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
    toast.success(`Block "${block.name}" added to newsletter`)
  }

  const handleVariableChange = (blockId: string, variableId: string, language: string, value: string) => {
    if (!displayData) return

    const updatedBlocks = displayData.nl_blocks.map(block => {
      if (block.id === blockId) {
        const updatedVariables = block.variable_values.map(variable => {
          if (variable.id === variableId) {
            return {
              ...variable,
              value: {
                ...variable.value,
                [language]: value
              }
            }
          }
          return variable
        })
        return { ...block, variable_values: updatedVariables }
      }
      return block
    })

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
  }

  const handleHtmlContentChange = (blockId: string, htmlContent: string) => {
    if (!displayData) return

    const updatedBlocks = displayData.nl_blocks.map(block => {
      if (block.id === blockId) {
        return { ...block, html_content: htmlContent }
      }
      return block
    })

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
  }

  const handleHeadersReorder = (reorderedHeaders: NewsletterHeaderFooter[]) => {
    if (!displayData) return

    setLocalNewsletterData({
      ...displayData,
      headers: reorderedHeaders
    })
    setHasUnsavedChanges(true)
  }

  const handleFootersReorder = (reorderedFooters: NewsletterHeaderFooter[]) => {
    if (!displayData) return

    setLocalNewsletterData({
      ...displayData,
      footers: reorderedFooters
    })
    setHasUnsavedChanges(true)
  }

  const handleHeaderFooterAIClick = (headerFooter: NewsletterHeaderFooter, type: 'header' | 'footer') => {
    setSelectedBlock(null) // Clear any selected block
    setAiDebugInfo({
      type: 'header_footer',
      headerFooterId: headerFooter.id,
      headerFooterType: type,
      name: headerFooter.name,
      newsletterId,
      brand: displayData?.brand_name
    })
    setAiDrawerOpen(true)
  }

  const handleHeaderFooterVariableChange = (headerFooterId: string, variableId: string, language: string, value: string) => {
    if (!displayData) return

    // Update headers
    const updatedHeaders = displayData.headers.map(header => {
      if (header.id === headerFooterId) {
        const updatedVariables = header.variable_values.map(variable => {
          if (variable.id === variableId) {
            return {
              ...variable,
              value: {
                ...variable.value,
                [language]: value
              }
            }
          }
          return variable
        })
        return { ...header, variable_values: updatedVariables }
      }
      return header
    })

    // Update footers
    const updatedFooters = displayData.footers.map(footer => {
      if (footer.id === headerFooterId) {
        const updatedVariables = footer.variable_values.map(variable => {
          if (variable.id === variableId) {
            return {
              ...variable,
              value: {
                ...variable.value,
                [language]: value
              }
            }
          }
          return variable
        })
        return { ...footer, variable_values: updatedVariables }
      }
      return footer
    })

    setLocalNewsletterData({
      ...displayData,
      headers: updatedHeaders,
      footers: updatedFooters
    })
    setHasUnsavedChanges(true)
  }

  const handleHeaderFooterHtmlContentChange = (headerFooterId: string, htmlContent: string) => {
    if (!displayData) return

    // Update headers
    const updatedHeaders = displayData.headers.map(header => {
      if (header.id === headerFooterId) {
        return { ...header, html_content: htmlContent }
      }
      return header
    })

    // Update footers
    const updatedFooters = displayData.footers.map(footer => {
      if (footer.id === headerFooterId) {
        return { ...footer, html_content: htmlContent }
      }
      return footer
    })

    setLocalNewsletterData({
      ...displayData,
      headers: updatedHeaders,
      footers: updatedFooters
    })
    setHasUnsavedChanges(true)
  }





  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Spinner className="h-8 w-8 mx-auto mb-4" variant='infinite'/>
            <p className="text-muted-foreground">Carregant...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Button onClick={refetch} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (!newsletterData) {
    return (
      <div className="p-6 space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Newsletter not found
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Newsletter Builder
              {hasUnsavedChanges && (
                <span className="ml-2 text-orange-500 text-lg">•</span>
              )}
            </h1>
            <p className="text-muted-foreground">
              {displayData?.name || 'Loading...'} • {displayData?.brand_name || 'Loading...'}
              {hasUnsavedChanges && (
                <span className="ml-2 text-orange-500 text-sm">• Unsaved changes</span>
              )}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={saving || !hasUnsavedChanges}
          >
            {saving ? (
              <Spinner className="h-4 w-4 mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Guardar
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {updateError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {updateError}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content - Two Panel Layout */}
      <div className="grid grid-cols-6 gap-6">
        {/* Left Panel - Block Management */}
        <div className="space-y-3 col-span-2">
          <BlockAccordion
            blocks={displayData?.nl_blocks || []}
            headers={displayData?.headers || []}
            footers={displayData?.footers || []}
            onBlocksReorder={handleBlocksReorder}
            onBlockAIClick={handleBlockAIClick}
            onHeadersReorder={handleHeadersReorder}
            onFootersReorder={handleFootersReorder}
            onHeaderFooterAIClick={handleHeaderFooterAIClick}
            onAddBlock={handleAddBlock}
            onVariableChange={handleVariableChange}
            onHtmlContentChange={handleHtmlContentChange}
            onHeaderFooterVariableChange={handleHeaderFooterVariableChange}
            onHeaderFooterHtmlContentChange={handleHeaderFooterHtmlContentChange}
            brandName={displayData?.brand_name}
            brand={displayData?.brand}
          />
        </div>

        {/* Right Panel - HTML Preview */}
        <div className="col-span-4 space-y-6 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto">
          <HTMLPreview
            blocks={displayData?.nl_blocks || []}
            headers={displayData?.headers || []}
            footers={displayData?.footers || []}
            className="h-full min-w-[600px]"
          />
        </div>
      </div>

      {/* Global AI Button */}
      <Button
        className="fixed bottom-6 right-6 shadow-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-110 group animate-pulse-gradient"
        size="icon"
        onClick={handleGlobalAIClick}
        style={{
          background: "linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)",
          backgroundSize: "400% 400%",
          animation: "gradient 15s ease infinite"
        }}
      >
        <Sparkles className="h-6 w-6 text-white group-hover:animate-pulse" />
      </Button>

      <Button
      className='fixed bottom-6 right-20'
      size={"icon"}
      >
        <Languages className="h-6 w-6 text-white group-hover:animate-pulse" />
      </Button>

      {/* AI Chat Drawer */}
      <AIChatDrawer
        open={aiDrawerOpen}
        onOpenChange={setAiDrawerOpen}
        block={selectedBlock}
        debugInfo={aiDebugInfo}
      />
    </div>
  )
}

export default NewsletterBuilderPage
