// Translation system for newsletter components
// Currently focused on Catalan translations

export interface TranslationKey {
  [key: string]: string | TranslationKey
}

export const translations: TranslationKey = {
  newsletter: {
    // HTML Preview Component
    livePreview: "Vista prèvia en directe",
    noBlocksToDisplay: "No hi ha blocs per mostrar",
    
    // Block Accordion Component
    newsletterContent: "Contingut de la newsletter",
    variables: "Variables",
    noVariablesDefined: "No s'han definit variables",
    htmlContent: "Contingut HTML",
    enterHtmlContentBlock: "Introdueix el contingut HTML per a aquest bloc",
    enterHtmlContentHeaderFooter: "Introdueix el contingut HTML per a aquesta capçalera/peu de pàgina",
    noDescription: "Sense descripció",
    addBlock: "Afegir bloc",
    headers: "Capçaleres",
    contentBlocks: "Blocs de contingut",
    footers: "Peus de pàgina",
    
    // Header Footer Item Component
    newsletterHeader: "Capçalera de la newsletter",
    newsletterFooter: "Peu de pàgina de la newsletter",
    
    // Language Switcher Component
    loading: "Carregant...",
    language: "Idioma",
    
    // Placeholder templates
    enterVariableIn: (variableName: string, language: string) => 
      `Introdueix ${variableName} en ${language}`,
    
    // Count templates
    headerCount: (count: number) => 
      `${count} capçaler${count !== 1 ? 'es' : 'a'}`,
    blockCount: (count: number) => 
      `${count} bloc${count !== 1 ? 's' : ''}`,
    footerCount: (count: number) => 
      `${count} peu${count !== 1 ? 's' : ''} de pàgina`,
    
    // Brand template
    forBrand: (brandName: string) => `per a ${brandName}`,
  },
  
  // Common UI elements
  common: {
    save: "Desar",
    cancel: "Cancel·lar",
    edit: "Editar",
    delete: "Eliminar",
    create: "Crear",
    update: "Actualitzar",
    close: "Tancar",
    open: "Obrir",
    yes: "Sí",
    no: "No",
    ok: "D'acord",
    error: "Error",
    success: "Èxit",
    warning: "Advertència",
    info: "Informació",
  }
}

// Helper function to get nested translation values
export function getTranslation(key: string, fallback?: string): string {
  const keys = key.split('.')
  let current: any = translations
  
  for (const k of keys) {
    if (current && typeof current === 'object' && k in current) {
      current = current[k]
    } else {
      return fallback || key
    }
  }
  
  return typeof current === 'string' ? current : fallback || key
}

// Shorthand function for translations
export const t = getTranslation

// Template function for dynamic translations
export function getTranslationTemplate(key: string): (...args: any[]) => string {
  const keys = key.split('.')
  let current: any = translations
  
  for (const k of keys) {
    if (current && typeof current === 'object' && k in current) {
      current = current[k]
    } else {
      return () => key
    }
  }
  
  return typeof current === 'function' ? current : () => key
}

// Shorthand function for template translations
export const tt = getTranslationTemplate
